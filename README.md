# 用户行为数据收集服务

这是一个基于Node.js和MySQL的用户行为数据收集服务。

## 功能特点

- 用户数据收集和管理
- 用户组数据管理
- RESTful API接口
- MySQL数据库存储
- GitLab API 认证

## 数据模型

### 用户行为表 (users_activity)
- user_name: 用户名
- prompt: 用户提示词
- ide: 用户使用的IDE

### 用户组表 (user_groups)
- user_name: 用户名
- user_tl: 用户组长
- effect_time: 生效时间

## 安装和运行

### 前提条件
- Node.js (v14+)
- MySQL 5.7

### 安装步骤

1. 克隆仓库
```
git clone <仓库地址>
cd user-stats-collector
```

2. 安装依赖
```
npm install
```

3. 配置环境变量
```
cp .env.example .env
```
然后编辑.env文件，填入以下信息：
- 数据库连接信息
- GitLab API URL
- AES加密密钥（用于用户名加密，使用ECB模式）

4. 启动服务
```
npm run dev
```

## API接口

API接口认证分为两种方式：

1. **GitLab认证**（用于 `/api/auth` 和 `/api/auth/test` 接口）：
```
Authorization: Bearer <your_gitlab_access_token>
```

2. **令牌认证**（用于 `/api/user-activity` 和 `/api/user-groups` 接口）：
```
Authorization: Bearer <encrypted_username_token>
```
其中 `<encrypted_username_token>` 是通过 `/api/auth` 接口获取的加密用户名令牌。

### 认证API

- GET /api/auth/test - 测试GitLab认证，返回用户信息
- GET /api/auth - 获取AES-ECB加密后的用户名令牌（十六进制格式）

### 用户活动API（需要令牌认证）

- GET /api/user-activity - 获取当前用户的所有活动记录（支持分页）
  - 分页参数：`page`（页码，默认1）、`pageSize`（每页条数，默认10）
  - 排序参数：`sortField`（排序字段，默认created_at）、`sortOrder`（排序方式，默认DESC）
  - 搜索参数：`search`（搜索关键词，会匹配prompt和ide字段）
  - 响应示例：
  ```json
  {
    "success": true,
    "count": 25,
    "data": [...],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "totalPages": 3,
      "total": 25
    },
    "sort": {
      "field": "created_at",
      "order": "DESC"
    }
  }
  ```
- GET /api/user-activity/:id - 获取当前用户的单个活动记录
- POST /api/user-activity - 创建当前用户的活动记录（自动填充用户名）
- PUT /api/user-activity/:id - 更新当前用户的活动记录
- DELETE /api/user-activity/:id - 删除当前用户的活动记录

### 用户组API（需要令牌认证）

- GET /api/user-groups - 获取当前用户的所有用户组
- GET /api/user-groups/:id - 获取当前用户的单个用户组
- POST /api/user-groups - 创建当前用户的用户组（自动填充用户名）
- PUT /api/user-groups/:id - 更新当前用户的用户组
- DELETE /api/user-groups/:id - 删除当前用户的用户组

## 开发

- `npm run dev` - 使用nodemon运行开发环境
- `npm start` - 运行生产环境
