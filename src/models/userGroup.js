const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// 用户组表模型
const UserGroup = sequelize.define('UserGroup', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  user_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '用户名'
  },
  user_tl: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: '',
    comment: '用户组长'
  },
  effect_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '生效时间'
  }
}, {
  tableName: 'user_groups',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = UserGroup;
