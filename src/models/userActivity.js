const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// 用户活动表模型
const UserActivity = sequelize.define('UserActivity', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false
  },
  user_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '用户名'
  },
  prompt: {
    type: DataTypes.TEXT,
    allowNull: true,
    defaultValue: '',
    comment: '用户提示词'
  },
  ide: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: '',
    comment: '用户使用的IDE'
  },
  mcp_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: '',
    comment: 'MCP名称'
  },
	page_type: {
    type: DataTypes.STRING(10),
    allowNull: true,
    defaultValue: '',
    comment: '页面类型'
  },
	complexity: {
    type: DataTypes.STRING(10),
    allowNull: true,
    defaultValue: '',
    comment: '复杂度'
  },
  file_path: {
    type: DataTypes.STRING(200),
    allowNull: true,
    defaultValue: '',
    comment: '文件相对路径'
  },
  git_store: {
    type: DataTypes.STRING(500),
    allowNull: true,
    defaultValue: '',
    comment: 'git仓库路径'
  }
}, {
  tableName: 'user_activity',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = UserActivity;
