const EncryptionUtil = require('../utils/encryption');
const { UserGroup } = require('../models');
require('dotenv').config();

/**
 * 基于加密令牌的认证中间件
 * 使用 /api/auth 生成的加密用户名令牌进行认证
 * 请求头格式: Authorization: Bearer <encrypted_username_token>
 */
const tokenAuth = async (req, res, next) => {
  try {
    // 获取请求头中的 Authorization
    const authHeader = req.headers.authorization;

    // 检查 Authorization 头是否存在
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '未提供访问令牌'
      });
    }

    // 提取访问令牌
    const token = authHeader.split(' ')[1];

    // 检查令牌是否为空
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌不能为空'
      });
    }

    try {
      // 使用 AES-ECB 解密令牌，获取用户名
      const username = EncryptionUtil.decryptAES(token);

      if (!username) {
        return res.status(401).json({
          success: false,
          message: '无效的访问令牌'
        });
      }

      // 查询数据库，验证用户是否存在
      const user = await UserGroup.findOne({ where: { user_name: username } });

      if (!user) {
        // 如果用户不存在，但令牌解密成功，则自动创建用户记录
        // 这是可选的，取决于您的业务需求
        const newUser = await UserGroup.create({
          user_name: username,
        });

        // 将用户信息添加到请求对象中
        req.user = newUser;
        req.username = username;
        next();
      } else {
        // 将用户信息添加到请求对象中
        req.user = user;
        req.username = username;
        next();
      }
    } catch (error) {
      console.error('令牌解密失败:', error);
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌'
      });
    }
  } catch (error) {
    console.error('令牌认证失败:', error);
    return res.status(500).json({
      success: false,
      message: '认证过程中发生错误'
    });
  }
};

module.exports = {
  tokenAuth
};
