const { sequelize } = require('../config/database');
const { UserActivity, UserGroup } = require('../models');

// 初始化数据库
const initDb = async () => {
  try {
    // 同步所有模型
    await sequelize.sync({ alter: true });
    console.log('数据库表同步完成');
    
    // 检查是否需要添加初始数据
    const userCount = await UserActivity.count();
    if (userCount === 0) {
      console.log('添加初始用户数据...');
      await UserActivity.bulkCreate([
        {
          user_name: 'test_user1',
          prompt: '测试提示词1',
          ide: 'VSCode'
        },
        {
          user_name: 'test_user2',
          prompt: '测试提示词2',
          ide: 'IntelliJ'
        }
      ]);
    }
    
    const groupCount = await UserGroup.count();
    if (groupCount === 0) {
      console.log('添加初始用户组数据...');
      await UserGroup.bulkCreate([
        {
          user_name: 'test_user1',
          user_tl: 'admin',
          effect_time: new Date()
        },
        {
          user_name: 'test_user2',
          user_tl: 'admin',
          effect_time: new Date()
        }
      ]);
    }
    
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
};

module.exports = initDb;
