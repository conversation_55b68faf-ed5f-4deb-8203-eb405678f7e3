const { UserGroup } = require('../models');
const Joi = require('joi');

// 验证schema
const userGroupSchema = Joi.object({
  user_name: Joi.string().required(),
  user_tl: Joi.string().required(),
  effect_time: Joi.date().default(new Date())
});

// 获取所有用户组
exports.getAllUserGroups = async (req, res) => {
  try {
    const userGroups = await UserGroup.findAll();
    res.status(200).json({
      success: true,
      count: userGroups.length,
      data: userGroups
    });
  } catch (error) {
    console.error('获取用户组列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 获取单个用户组
exports.getUserGroupByUserName = async (req, res) => {
  try {
    const userGroup = await UserGroup.findByPk(req.params.user_name);
    
    if (!userGroup) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户组'
      });
    }

    res.status(200).json({
      success: true,
      data: userGroup
    });
  } catch (error) {
    console.error('获取用户组详情失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 创建用户组
exports.createUserGroup = async (req, res) => {
  try {
    // 验证请求数据
    const { error, value } = userGroupSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const userGroup = await UserGroup.create(value);
    
    res.status(201).json({
      success: true,
      data: userGroup
    });
  } catch (error) {
    console.error('创建用户组失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 更新用户组
exports.updateUserGroup = async (req, res) => {
  try {
    // 验证请求数据
    const { error, value } = userGroupSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const userGroup = await UserGroup.findByPk(req.params.user_name);
    
    if (!userGroup) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户组'
      });
    }

    await userGroup.update(value);
    
    res.status(200).json({
      success: true,
      data: userGroup
    });
  } catch (error) {
    console.error('更新用户组失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 删除用户组
exports.deleteUserGroup = async (req, res) => {
  try {
    const userGroup = await UserGroup.findByPk(req.params.user_name);
    
    if (!userGroup) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户组'
      });
    }

    await userGroup.destroy();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('删除用户组失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};
