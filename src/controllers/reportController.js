
const { UserActivity, UserGroup } = require('../models');
const Joi = require('joi');
const { Sequelize } = require('sequelize');

// 验证schema
const reportQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(1000).default(10),
  sortField: Joi.string().valid('created_at', 'user_name', 'prompt', 'ide', 'mcp_name', 'complexity').default('created_at'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso().min(Joi.ref('startDate'))
});

// 获取用户活动报表（关联用户组信息）
exports.getUserActivityReport = async (req, res) => {
  try {
    // 验证查询参数
    const { error, value } = reportQuerySchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // 解构验证后的查询参数
    const { page, pageSize, sortField, sortOrder, startDate, endDate } = value;
    const offset = (page - 1) * pageSize;

    // 构建查询条件
    const where = {};
    
    // 如果提供了日期范围，添加到查询条件
    if (startDate && endDate) {
      where.created_at = {
        [Op.between]: [startDate, endDate]
      };
    } else if (startDate) {
      where.created_at = {
        [Op.gte]: startDate
      };
    } else if (endDate) {
      where.created_at = {
        [Op.lte]: endDate
      };
    }

    // 查询总记录数
    const total = await UserActivity.count({ where });

    // 查询当前页数据，并关联用户组信息
    const userActivities = await UserActivity.findAll({
      where,
      include: [
        {
          model: UserGroup,
          as: 'userGroup',
          attributes: ['user_tl'], // 只获取user_tl字段
          exclude: ['id'],
          required: false // 使用LEFT JOIN
        }
      ],
      exclude: ['userGroup.id'],
      order: [[sortField, sortOrder]],
      limit: pageSize,
      offset: offset
    });

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    res.status(200).json({
      success: true,
      count: total,
      data: userActivities,
      pagination: {
        page,
        pageSize,
        totalPages,
        total
      },
      sort: {
        field: sortField,
        order: sortOrder
      }
    });
  } catch (error) {
    console.error('获取用户活动报表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取用户活动统计数据
exports.getUserActivityStats = async (req, res) => {
  try {
    // 获取按用户组长(user_tl)分组的活动数量
    const statsByUserTL = await UserActivity.findAll({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('UserActivity.id')), 'activity_count']
      ],
      include: [
        {
          model: UserGroup,
          as: 'userGroup',
          attributes: ['user_tl'],
          exclude: ['user_name'],
          required: false
        }
      ],
      group: ['userGroup.user_tl']
    });

    // 获取按IDE分组的活动数量
    const statsByIDE = await UserActivity.findAll({
      attributes: [
        'ide',
        [Sequelize.fn('COUNT', Sequelize.col('UserActivity.id')), 'count']
      ],
      where: {
        ide: {
          [Op.not]: null,
          [Op.ne]: ''
        }
      },
      group: ['ide']
    });

    res.status(200).json({
      success: true,
      data: {
        byUserTL: statsByUserTL,
        byIDE: statsByIDE
      }
    });
  } catch (error) {
    console.error('获取用户活动统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

