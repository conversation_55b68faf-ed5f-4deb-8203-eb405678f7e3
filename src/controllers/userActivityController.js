const { UserActivity } = require('../models');
// const { Sequelize } = require('sequelize');
const Joi = require('joi');

// 验证schema - 完整版（包含用户名）
const userSchema = Joi.object({
  user_name: Joi.string().required(),
  prompt: Joi.string().allow('', null),
  ide: Joi.string().allow('', null),
  mcp_name: Joi.string().allow('', null),
  complexity: Joi.string().allow('', null),
  page_type: Joi.string().allow('', null),
  file_path: Joi.string().allow('', null),
  git_store: Joi.string().allow('', null)
});

// 验证schema - 不包含用户名，用于自动填充当前登录用户
const userSchemaWithoutUsername = Joi.object({
  prompt: Joi.string().allow('', null),
  ide: Joi.string().allow('', null),
  mcp_name: Joi.string().allow('', null),
  complexity: Joi.string().allow('', null),
  page_type: Joi.string().allow('', null),
  file_path: Joi.string().allow('', null),
  git_store: Joi.string().allow('', null)
});

// 获取当前用户的所有活动记录（支持分页）
exports.getAllUserActivitys = async (req, res) => {
  try {
    // 获取当前登录用户名
    const username = req.username;

    // 获取分页参数
    const page = parseInt(req.query.page) || 1; // 默认第1页
    const pageSize = parseInt(req.query.pageSize) || 10; // 默认每页10条
    const offset = (page - 1) * pageSize;

    // 获取排序参数
    const sortField = req.query.sortField || 'created_at'; // 默认按创建时间排序
    const sortOrder = req.query.sortOrder || 'DESC'; // 默认降序排序

    // 查询条件
    const where = { user_name: username };

    // // 如果有搜索参数，添加到查询条件中
    // if (req.query.search) {
    //   where[Sequelize.Op.or] = [
    //     { prompt: { [Sequelize.Op.like]: `%${req.query.search}%` } },
    //     { ide: { [Sequelize.Op.like]: `%${req.query.search}%` } }
    //   ];
    // }

    // 查询总记录数
    const total = await UserActivity.count({ where });

    // 查询当前页数据
    const userActivitys = await UserActivity.findAll({
      where,
      order: [[sortField, sortOrder]],
      limit: pageSize,
      offset: offset
    });

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    res.status(200).json({
      success: true,
      count: total,
      data: userActivitys,
      pagination: {
        page,
        pageSize,
        totalPages,
        total
      },
      sort: {
        field: sortField,
        order: sortOrder
      }
    });
  } catch (error) {
    console.error('获取用户活动列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取单个用户活动记录
exports.getUserActivityById = async (req, res) => {
  try {
    // 获取当前登录用户名
    const username = req.username;

    const userActivity = await UserActivity.findByPk(req.params.id);

    if (!userActivity) {
      return res.status(404).json({
        success: false,
        message: '未找到该记录'
      });
    }

    // 检查是否是当前用户的记录
    if (userActivity.user_name !== username) {
      return res.status(403).json({
        success: false,
        message: '无权查看其他用户的记录'
      });
    }

    res.status(200).json({
      success: true,
      data: userActivity
    });
  } catch (error) {
    console.error('获取用户活动详情失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 创建用户
exports.createUserActivity = async (req, res) => {
  try {
    // 验证请求数据（不包含用户名）
    const { error, value } = userSchemaWithoutUsername.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // 使用令牌中间件提供的用户名
    const username = req.username;
    if (!username) {
      return res.status(400).json({
        success: false,
        message: '无法获取用户名'
      });
    }

    // 创建用户活动记录，自动填充用户名
    const userData = {
      ...value,
      user_name: username
    };
    
    if (userData.file_path && userData.file_path.length > 200) {
      userData.file_path = userData.file_path.substring(0, 200);
    } 

    const user = await UserActivity.create(userData);

    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('创建用户活动记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 更新用户
exports.updateUserActivity = async (req, res) => {
  try {
    // 验证请求数据（不包含用户名）
    const { error, value } = userSchemaWithoutUsername.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    // 获取当前登录用户名
    const username = req.username;

    // 查找要更新的记录
    const user = await UserActivity.findByPk(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该记录'
      });
    }

    // 检查是否是当前用户的记录
    if (user.user_name !== username) {
      return res.status(403).json({
        success: false,
        message: '无权更新其他用户的记录'
      });
    }

    // 更新记录，但不允许更改用户名
    await user.update(value);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('更新用户活动记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

// 删除用户
exports.deleteUserActivity = async (req, res) => {
  try {
    // 获取当前登录用户名
    const username = req.username;

    const user = await UserActivity.findByPk(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该记录'
      });
    }

    // 检查是否是当前用户的记录
    if (user.user_name !== username) {
      return res.status(403).json({
        success: false,
        message: '无权删除其他用户的记录'
      });
    }

    await user.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('删除用户活动记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};
