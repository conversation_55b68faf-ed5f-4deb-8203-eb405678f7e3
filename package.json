{"name": "user-stats-collector", "version": "1.0.0", "description": "服务用于收集用户使用行为数据", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["user", "stats", "collector"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "morgan": "^1.10.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.1.10"}}